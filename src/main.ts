import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app/app.module.js";
import cookieParser from "cookie-parser";
import { useRequestLogging } from "./shared/middlewares/request-logger.js";
import { patchNestJsSwagger } from "nestjs-zod";
import pg from "pg";
import { setupSwaggerDocumentation } from "./docs/setup/swagger.setup.js";
import { Logger } from "@nestjs/common";
import { ZodValidationExceptionFilter } from "./shared/filters/zod-exception.filter.js";
import { PostgresExceptionFilter } from "./shared/filters/pg-exception.filter.js";

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: {
      credentials: true,
      origin: [
        "http://localhost:5173",
        "http://localhost:5174",
        "http://localhost:5175",
        "http://localhost:3000",
      ],
    },
  });

  // Override default parser for numeric types
  const NUMERIC_OID = 1700;
  pg.types.setTypeParser(NUMERIC_OID, val => {
    return parseFloat(val);
  });
  // Override default parser for bigint types
  const BIGINT_OID = 20;
  pg.types.setTypeParser(BIGINT_OID, val => {
    const num = BigInt(val);
    if (num < BigInt(Number.MAX_SAFE_INTEGER)) {
      return Number(num);
    }
    throw new Error("Number exceeds maximum supported value (2^53-1)");
  });

  // set express cookie parser
  app.use(cookieParser());

  // set global prefix for all routes
  app.setGlobalPrefix("/v1");

  // limit logging level in production
  if (process.env.NODE_ENV === "production") {
    app.useLogger(["error", "warn"]);
  }

  // set global exception filters
  app.useGlobalFilters(
    new ZodValidationExceptionFilter(),
    new PostgresExceptionFilter(),
  );

  // set request logger
  useRequestLogging(app);

  // add zod schema support for open api docs
  patchNestJsSwagger();

  // build swagger open api docs
  setupSwaggerDocumentation(app);

  // start the server
  await app.listen(process.env.PORT ?? 3000, process.env.HOST ?? "0.0.0.0");

  Logger.log(`Server listening at ${await app.getUrl()}`, "Main");
}
void bootstrap();
