import { HttpStatus } from "@nestjs/common";

export interface ApiErrorResponse {
  statusCode: number;
  message: string;
  error: string;
}

export function createBadRequestResponse(message: string): ApiErrorResponse {
  return {
    statusCode: HttpStatus.BAD_REQUEST,
    message,
    error: "Bad Request",
  };
}

export function createNotFoundResponse(message: string): ApiErrorResponse {
  return {
    statusCode: HttpStatus.NOT_FOUND,
    message,
    error: "Not Found",
  };
}

export function createConflictResponse(message: string): ApiErrorResponse {
  return {
    statusCode: HttpStatus.CONFLICT,
    message,
    error: "Conflict",
  };
}

export function createInternalServerErrorResponse(
  message: string,
): ApiErrorResponse {
  return {
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message,
    error: "Internal Server Error",
  };
}

export function createForbiddenResponse(message: string): ApiErrorResponse {
  return {
    statusCode: HttpStatus.FORBIDDEN,
    message,
    error: "Forbidden",
  };
}
