import { Module } from "@nestjs/common";
import { EnrollmentsService } from "./enrollments.service.js";
import { EnrollmentsController } from "./enrollments.controller.js";
import { AcademicSessionsModule } from "../academic-sessions/academic-sessions.module.js";
import { ClassSectionsModule } from "../classes/sections/class-sections.module.js";

@Module({
  imports: [AcademicSessionsModule, ClassSectionsModule],
  controllers: [EnrollmentsController],
  providers: [EnrollmentsService],
  exports: [EnrollmentsService],
})
export class EnrollmentsModule {}
