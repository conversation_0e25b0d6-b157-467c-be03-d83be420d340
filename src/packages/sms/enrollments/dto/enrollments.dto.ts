import { z } from "zod";
import {
  dateSchema,
  getUuidSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

const enrollmentBaseSchema = z.object({
  studentId: getUuidSchema("Student ID"),
  classSectionId: getUuidSchema("Class Section ID"),
  academicSessionId: getUuidSchema("Academic Session ID"),
  type: z.enum(["ADMISSION", "TRANSFER_IN", "REPEATING", "PROMOTION"], {
    message:
      "Enrollment type must be one of 'ADMISSION', 'TRANSFER_IN', 'REPEATING', or 'PROMOTION'.",
  }),
  status: z.enum(
    ["ACTIVE", "EXPELLED", "GRADUATED", "DECEASED", "COMPLETED", "WITHDRAWN"],
    {
      message:
        "Enrollment status must be one of 'ACTIVE', 'EXPELLED', 'GRADUATED', 'DECEASED', 'COMPLETED', or 'WITHDRAWN'.",
    },
  ),
  date: dateSchema,
});

// ------------------- Create-Enrollment-Dto ------------------->
const createEnrollmentSchema = enrollmentBaseSchema.omit({
  academicSessionId: true,
});
export class CreateEnrollmentDto extends createZodDto(createEnrollmentSchema) {}
