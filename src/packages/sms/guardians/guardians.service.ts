import { ConflictException, Injectable, Logger } from "@nestjs/common";
import { Kysely, Transaction } from "kysely";
import { DB } from "../../../database/types.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { CreateGuardianPayload, Guardian } from "./types/guardian.types.js";
import { omitKeysFromObject } from "../../../utils/object-utils.js";
import { UsersService } from "../../../core/users/users.service.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { EntityIdResponse } from "../../../shared/types/common.types.js";

@Injectable()
export class GuardiansService {
  private readonly logger = new Logger(GuardiansService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly userService: UsersService,
    private readonly rolesService: RolesService,
  ) {}

  public async findById(id: Guardian["userId"]) {
    try {
      return await this.db
        .selectFrom("guardian")
        .selectAll()
        .where("userId", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find guardian by user id", error);
      throw error;
    }
  }

  public async create(
    createGuardianPayload: CreateGuardianPayload,
    parentTrx?: Transaction<DB>,
  ): Promise<EntityIdResponse> {
    try {
      const userInfo = omitKeysFromObject(createGuardianPayload, ["relation"]);

      return await this.db.transaction().execute(async guardianTrx => {
        const activeTrx = parentTrx ?? guardianTrx;

        const guardianRole =
          await this.rolesService.findByNameOrCreate("GUARDIAN");

        const { id: userAccountId } = await this.userService.create(
          {
            ...userInfo,
            roleId: guardianRole.id,
          },
          activeTrx,
        );

        const { userId: guardianId } = await activeTrx
          .insertInto("guardian")
          .values({
            relation: createGuardianPayload.relation,
            userId: userAccountId,
          })
          .returning(["userId"])
          .executeTakeFirstOrThrow();

        return { id: guardianId };
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create guardian");

      if (error instanceof ConflictException) {
        throw new ConflictException("Guardian with same email already exists");
      }

      handleDatabaseInsertException(error, {
        resource: "guardian",
        logger: this.logger,
      });
    }
  }
}
