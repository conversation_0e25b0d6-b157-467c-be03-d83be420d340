import { createZodDto } from "nestjs-zod";
import { baseUserSchema } from "../../../../core/users/dto/user.dto.js";
import { z } from "zod";

export const guardianBaseSchema = baseUserSchema.extend({
  relation: z.enum(["<PERSON>TH<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "GUARDIAN"], {
    message:
      "Guardian relation must be one of 'FATHER', 'MOTHER', or 'GUARDIAN'.",
  }),
});

// ------------------- Create-Guardian-Dto ------------------->
export const createGuardianSchema = guardianBaseSchema;
export class CreateGuardianDto extends createZodDto(guardianBaseSchema) {}
