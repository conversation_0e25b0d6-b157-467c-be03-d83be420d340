import { createZodDto } from "nestjs-zod";
import { z } from "zod";

const subjectsBaseSchema = z.object({
  name: z
    .string()
    .nonempty()
    .max(100, { message: "Name cannot exceed 100 characters" }),
  type: z.enum(["THEORY", "PRACTICAL"], {
    message: "Type must be one of 'THEORY' or 'PRACTICAL'.",
  }),
  marks: z
    .number({ message: "Subject marks are required" })
    .min(1, { message: "Subject marks cannot be less than 1" })
    .max(1000, { message: "Subject marks cannot exceed 1000" }),
});

// ------------------- Create-Subject-Dto ------------------->

export class CreateSubjectDto extends createZodDto(subjectsBaseSchema) {}

// ------------------- Update-Subject-Dto ------------------->
export const updateSubjectSchema = subjectsBaseSchema.partial();
export class UpdateSubjectDto extends createZodDto(updateSubjectSchema) {}
