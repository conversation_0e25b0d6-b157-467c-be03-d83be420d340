import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { SubjectsService } from "./subjects.service.js";
import { CreateSubjectDto, UpdateSubjectDto } from "./dto/subjects.dto.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
import { SessionSubjectParamDto } from "./dto/subjects-param.dto.js";

@ApiTags("Subjects")
@Controller("sms/sessions")
export class SubjectsController {
  public constructor(private readonly subjectService: SubjectsService) {}

  @Roles(["INSTITUTE_OWNER"])
  @Get(":sessionId/subjects")
  public async getAll(
    @Param() param: AcademicSessionIdParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.subjectService.findAll(param.sessionId, query);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Post(":sessionId/subjects")
  public create(
    @Body() createSubjectDto: CreateSubjectDto,
    @Param() param: AcademicSessionIdParamDto,
  ) {
    return this.subjectService.create({
      ...createSubjectDto,
      academicSessionId: param.sessionId,
    });
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch(":sessionId/subjects/:subjectId")
  public async update(
    @Param() param: SessionSubjectParamDto,
    @Body() updateSubjectDto: UpdateSubjectDto,
  ) {
    return await this.subjectService.update(param.subjectId, updateSubjectDto);
  }
}
