import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { NewSubject, Subject, SubjectUpdate } from "./types/subject.type.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/common.types.js";

@Injectable()
export class SubjectsService {
  private logger = new Logger(SubjectsService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly academicSessionsService: AcademicSessionsService,
  ) {}

  public async findAll(
    sessionId: AcademicSession["id"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, { total }] = await Promise.all([
        await this.db
          .selectFrom("subject")
          .selectAll()
          .where("academicSessionId", "=", sessionId)
          .limit(queryOptions.limit)
          .offset(queryOptions.offset)
          .orderBy("createdAt", "desc")
          .execute(),

        await this.db
          .selectFrom("subject")
          .select(({ fn }) => [fn.count("subject.id").as("total")])
          .where("academicSessionId", "=", sessionId)
          .executeTakeFirstOrThrow(),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all subjects", error);
      throw error;
    }
  }

  public async create(newSubjectPayload: NewSubject): Promise<Subject> {
    try {
      await this.verifyAcademicSessionExists(
        newSubjectPayload.academicSessionId,
      );

      return await this.db
        .insertInto("subject")
        .values(newSubjectPayload)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subject",
        logger: this.logger,
        messages: {
          uniqueConstraint: "Subject with same name already exists",
        },
      });
    }
  }

  public async update(
    subjectId: Subject["id"],
    updateSubjectPayload: SubjectUpdate,
  ) {
    try {
      const sessionId = updateSubjectPayload.academicSessionId;
      if (sessionId) {
        await this.verifyAcademicSessionExists(sessionId);
      }

      await this.verifySubjectExists(subjectId);

      return await this.db
        .updateTable("subject")
        .set(updateSubjectPayload)
        .where("id", "=", subjectId)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subject",
        logger: this.logger,
      });
    }
  }

  public async findById(id: Subject["id"]) {
    try {
      return await this.db
        .selectFrom("subject")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find subject by id", error);
      throw error;
    }
  }

  private async verifySubjectExists(subjectId: string) {
    let existingSubject: Subject | undefined;

    try {
      existingSubject = await this.findById(subjectId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify subject exists", error);
      throw error;
    }
    if (!existingSubject) {
      throw new NotFoundException(`Subject not found`);
    }
  }

  private async verifyAcademicSessionExists(academicSessionId: string) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession =
        await this.academicSessionsService.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }
}
