import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely, sql } from "kysely";
import { DB } from "../../../database/types.js";
import {
  Class,
  ClassResponse,
  CreateClassPayload,
  UpdateClassPayload,
} from "./types/classes.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import {
  EntityIdResponse,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/common.types.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ClassSectionsService } from "./sections/class-sections.service.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";

@Injectable()
export class ClassesService {
  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly classSectionsService: ClassSectionsService,
    private readonly academicSessionsService: AcademicSessionsService,
  ) {}
  public readonly logger = new Logger(ClassesService.name);

  public async create(createClassPayload: CreateClassPayload): Promise<string> {
    await this.verifyAcademicSessionExists(
      createClassPayload.academicSessionId,
    );

    try {
      const { sections, ...classPayload } = createClassPayload;

      return await this.db.transaction().execute(async trx => {
        const newClass = await trx
          .insertInto("class")
          .values(classPayload)
          .returning(["id"])
          .executeTakeFirstOrThrow();

        const sectionsPayload = sections.map(section => ({
          ...section,
          classId: newClass.id,
        }));

        await this.classSectionsService.createMany(sectionsPayload, { trx });

        return newClass.id;
      });
    } catch (error: unknown) {
      this.logger.error("Failed to create class", error);
      handleDatabaseInsertException(error, {
        resource: "classes",
        logger: this.logger,
      });
    }
  }

  public async update(
    classId: Class["id"],
    updateClassPayload: UpdateClassPayload,
  ): Promise<EntityIdResponse> {
    try {
      const existingClass = await this.findById(classId);
      if (!existingClass) {
        throw new NotFoundException(`Class with id: ${classId} does not found`);
      }

      const { sections, ...classPayload } = updateClassPayload;
      return await this.db.transaction().execute(async trx => {
        const { id: updatedClassId } = await trx
          .updateTable("class")
          .set(classPayload)
          .where("id", "=", classId)
          .returning("id")
          .executeTakeFirstOrThrow();

        if (sections) {
          for (const section of sections) {
            await this.classSectionsService.createOrUpdate(
              { ...section, classId: updatedClassId },
              { trx },
            );
          }
        }
        return { id: updatedClassId };
      });
    } catch (error) {
      this.logger.error("Failed to update class", error);
      handleDatabaseInsertException(error, {
        resource: "classes",
        logger: this.logger,
      });
    }
  }

  public async findAllByAcademicSessionId(
    sessionId: string,
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, { total }] = await Promise.all([
        await this.selectClassQuery()
          .where("class.academicSessionId", "=", sessionId)
          .limit(queryOptions.limit)
          .offset(queryOptions.offset)
          .orderBy("class.createdAt", "asc")
          .execute(),

        await this.db
          .selectFrom("class")
          .select(({ fn }) => [fn.count("class.id").as("total")])
          .where("class.academicSessionId", "=", sessionId)
          .executeTakeFirstOrThrow(),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all classes", error);
      throw error;
    }
  }

  public async findById(
    id: Class["id"],
    options?: ServiceOptions,
  ): Promise<ClassResponse | undefined> {
    try {
      return await this.selectClassQuery(options)
        .where("class.id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find class by id", error);
      throw error;
    }
  }

  private async verifyAcademicSessionExists(sessionId: string) {
    let existingSession: AcademicSession | undefined;
    try {
      existingSession = await this.academicSessionsService.findById(sessionId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify branch exists", error);
      throw error;
    }

    if (!existingSession) {
      throw new NotFoundException(`Session with id: ${sessionId} not found`);
    }
  }

  private selectClassQuery(options?: ServiceOptions) {
    const kyselyClient = options?.trx ?? this.db;
    return kyselyClient
      .selectFrom("class")
      .innerJoin("classSection", "classSection.classId", "class.id")
      .innerJoin("staff", "staff.id", "classSection.classTeacherId")
      .innerJoin("users", "staff.userId", "users.id")
      .select([
        "class.id",
        "class.name",
        "class.feePerMonth",
        "class.maximumStudents",
        "class.isActive",
        "class.createdAt",
        sql<ClassResponse["sections"]>`json_agg(json_build_object(
            'id', class_section.id,
            'name', class_section.name,
            'createdAt', class_section.created_at,
            'isActive', class_section.is_active,
            'classTeacher',json_build_object(
                  'id', users.id,
                  'name', users.name
               )
          ))`.as("sections"),
      ])
      .groupBy(["class.id"]);
  }
}
