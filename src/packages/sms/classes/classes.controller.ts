import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Res,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { AcademicSessionClassParamsDto } from "./dto/classes-params.dto.js";
import { CreateClassDto, UpdateClassDto } from "./dto/classes.dto.js";
import { ClassesService } from "./classes.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import type { Response } from "express";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
@Controller("sms/academic-sessions")
@ApiTags("Classes")
export class ClassesController {
  public constructor(private readonly classesService: ClassesService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/:sessionId/classes")
  public getAll(
    @Param() params: AcademicSessionIdParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.classesService.findAllByAcademicSessionId(
      params.sessionId,
      query,
    );
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/:sessionId/classes")
  public async create(
    @Param() param: AcademicSessionIdParamDto,
    @Body() createClassDto: CreateClassDto,
    @Res() res: Response,
  ) {
    const id = await this.classesService.create({
      ...createClassDto,
      academicSessionId: param.sessionId,
    });
    res.setHeader("Location", id);
    res.status(HttpStatus.CREATED).send();
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Patch("/:sessionId/classes/:classId")
  @HttpCode(HttpStatus.NO_CONTENT)
  public async update(
    @Param() param: AcademicSessionClassParamsDto,
    @Body() updateClassDto: UpdateClassDto,
  ): Promise<void> {
    await this.classesService.update(param.classId, updateClassDto);
  }
}
