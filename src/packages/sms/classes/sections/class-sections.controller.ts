import { Body, Controller, Get, Param, Post } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ClassSectionsService } from "./class-sections.service.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import { ClassSectionParamsDto } from "./dto/class-sections-params.dto.js";
import { CreateClassSectionDto } from "./dto/class-sections.dto.js";

@Controller("sms/classes")
@ApiTags("Class Sections")
export class ClassSectionsController {
  public constructor(private readonly sectionsService: ClassSectionsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/:classId/sections")
  public create(
    @Body() createClassSectionDto: CreateClassSectionDto,
    @Param() param: ClassSectionParamsDto,
  ) {
    return this.sectionsService.create({
      ...createClassSectionDto,
      classId: param.classId,
    });
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/:classId/sections")
  public getAllClassSections(@Param() param: ClassSectionParamsDto) {
    return this.sectionsService.getAllByClassId(param.classId);
  }
}
