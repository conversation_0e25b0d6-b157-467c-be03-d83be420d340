import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely } from "kysely";
import { DB } from "../../../../database/types.js";
import {
  ClassSection,
  ClassSectionUpdate,
  NewClassSection,
} from "./types/class-sections.types.js";
import { handleDatabaseInsertException } from "../../../../utils/pg-utils.js";
import { ClassesService } from "../classes.service.js";
import { Class, ClassResponse } from "../types/classes.types.js";
import { ModuleRef } from "@nestjs/core";
import { StaffService } from "../../staff/staff.service.js";
import { StaffProfile } from "../../staff/types/staff.types.js";
import {
  EntityIdResponse,
  ServiceOptions,
} from "../../../../shared/types/common.types.js";

@Injectable()
export class ClassSectionsService implements OnModuleInit {
  private readonly logger = new Logger(ClassSectionsService.name);

  private classesService: ClassesService;
  private staffService: StaffService;

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly moduleRef: ModuleRef,
  ) {}

  onModuleInit() {
    this.classesService = this.moduleRef.get(ClassesService, {
      strict: false,
    });
    this.staffService = this.moduleRef.get(StaffService, {
      strict: false,
    });
  }

  public async create(
    createSectionPayload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    await this.verifyClassExists(createSectionPayload.classId, options);

    try {
      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .insertInto("classSection")
        .values(createSectionPayload)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
        messages: {
          uniqueConstraint: "Section with same name already exists",
        },
      });
    }
  }

  public async update(
    id: ClassSection["id"],
    updateSectionPayload: ClassSectionUpdate,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      if (updateSectionPayload.classTeacherId) {
        await this.verifyClassTeacherExists(
          updateSectionPayload.classTeacherId,
          options,
        );
      }
      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .updateTable("classSection")
        .set(updateSectionPayload)
        .where("id", "=", id)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to update section", error);
      throw error;
    }
  }

  public async createOrUpdate(
    createSectionPayload: NewClassSection,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      const existingSection = await this.findClassSection(
        {
          name: createSectionPayload.name,
          classId: createSectionPayload.classId,
        },
        options,
      );

      if (existingSection) {
        return await this.update(
          existingSection.id,
          createSectionPayload,
          options,
        );
      }
      return await this.create(createSectionPayload, options);
    } catch (error: unknown) {
      this.logger.error("Failed to create or update section", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }
  /*
  |--------------------------------------------------------------------------
  | Create Many Sections
  |--------------------------------------------------------------------------
  |
  | This method is used to create multiple class sections in a single operation.
  | It is designed to be called internally from other services and not directly
  | from controllers. The method accepts an array of section data and inserts
  | them into the database, returning the created sections with their IDs.
  |
  */
  public async createMany(
    createManySectionPayload: NewClassSection[],
    options?: ServiceOptions,
  ): Promise<void> {
    for (const section of createManySectionPayload) {
      await this.verifyClassTeacherExists(section.classTeacherId, options);
    }

    try {
      const kyselyClient = options?.trx ?? this.db;
      await kyselyClient
        .insertInto("classSection")
        .values(createManySectionPayload)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to create sections", error);
      handleDatabaseInsertException(error, {
        resource: "classSection",
        logger: this.logger,
      });
    }
  }

  public async findById(id: ClassSection["id"]) {
    try {
      return await this.db
        .selectFrom("classSection")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find section by id", error);
      throw error;
    }
  }

  public async getAllByClassId(classId: Class["id"]) {
    try {
      return await this.db
        .selectFrom("classSection")
        .selectAll()
        .where("classId", "=", classId)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all sections", error);
      throw error;
    }
  }

  private async findClassSection(
    criteria: Partial<ClassSection>,
    options?: ServiceOptions,
  ) {
    const kyselyClient = options?.trx ?? this.db;
    let query = kyselyClient.selectFrom("classSection");

    if (criteria.id) {
      query = query.where("id", "=", criteria.id);
    }

    if (criteria.name) {
      query = query.where("name", "=", criteria.name);
    }

    if (criteria.classId) {
      query = query.where("classId", "=", criteria.classId);
    }

    if (criteria.classTeacherId) {
      query = query.where("classTeacherId", "=", criteria.classTeacherId);
    }

    if (criteria.isActive) {
      query = query.where("isActive", "=", criteria.isActive);
    }

    if (criteria.createdAt) {
      query = query.where("createdAt", "=", criteria.createdAt);
    }

    return query.selectAll().executeTakeFirst();
  }

  private async verifyClassTeacherExists(
    classTeacherId: string,
    options?: ServiceOptions,
  ) {
    let existingClassTeacher: StaffProfile | undefined;
    try {
      existingClassTeacher = await this.staffService.findInstitutionalStaffById(
        classTeacherId,
        options,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to verify class teacher exists", error);
      throw error;
    }

    if (!existingClassTeacher) {
      throw new NotFoundException("Class teacher not found");
    }
  }

  private async verifyClassExists(classId: string, options?: ServiceOptions) {
    let existingClass: ClassResponse | undefined;
    try {
      existingClass = await this.classesService.findById(classId, options);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class exists", error);
      throw error;
    }

    if (!existingClass) {
      throw new NotFoundException(`Class with id: ${classId} not found`);
    }
  }
}
