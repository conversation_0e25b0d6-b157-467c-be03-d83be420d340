import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { SubjectsService } from "../subjects/subjects.service.js";
import {
  EntityIdResponse,
  ListAllEntitiesQueryOptions,
  ServiceOptions,
} from "../../../shared/types/common.types.js";
import {
  NewSectionSubject,
  SectionSubject,
  SectionSubjectAssignmentResponse,
  SectionSubjectUpdate,
} from "./types/section-subjects.type.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { DB } from "../../../database/types.js";
import { Kysely, sql } from "kysely";
import { StaffService } from "../staff/staff.service.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { ClassSectionsService } from "../classes/sections/class-sections.service.js";

@Injectable()
export class SectionSubjectsService {
  private readonly logger = new Logger(SectionSubjectsService.name);

  constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly subjectsService: SubjectsService,
    private readonly staffService: StaffService,
    private readonly academicSessionsService: AcademicSessionsService,
    private readonly classSectionsService: ClassSectionsService,
  ) {}

  public async assignSubjectToSection(
    assignSubjectPayload: NewSectionSubject,
    options?: ServiceOptions,
  ): Promise<EntityIdResponse> {
    try {
      await this.verifyForeignResourcesExist(assignSubjectPayload);

      const kyselyClient = options?.trx ?? this.db;
      return await kyselyClient
        .insertInto("sectionSubject")
        .values(assignSubjectPayload)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to assign subject to section", error);
      handleDatabaseInsertException(error, {
        resource: "sectionSubject",
        logger: this.logger,
        messages: {
          uniqueConstraint: "Subject already assigned to section",
        },
      });
    }
  }

  public async updateSubjectAssignment(
    id: SectionSubject["id"],
    updateSectionSubjectPayload: SectionSubjectUpdate,
  ) {
    try {
      await this.verifyForeignResourcesExist(updateSectionSubjectPayload);

      return await this.db
        .updateTable("sectionSubject")
        .set(updateSectionSubjectPayload)
        .where("id", "=", id)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to update subject assignment", error);
      throw error;
    }
  }

  public async findAllByAcademicSessionId(
    academicSessionId: SectionSubject["academicSessionId"],
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      const [items, { total }] = await Promise.all([
        await this.selectSectionSubjectsQuery()
          .where("class.academicSessionId", "=", academicSessionId)
          .offset(queryOptions.offset)
          .limit(queryOptions.limit)
          .orderBy("sectionSubject.createdAt", "desc")
          .execute(),

        await this.db
          .selectFrom("sectionSubject")
          .select(({ fn }) => [fn.count("sectionSubject.id").as("total")])
          .where("academicSessionId", "=", academicSessionId)
          .executeTakeFirstOrThrow(),
      ]);
      return { items, total };
    } catch (error: unknown) {
      this.logger.error(
        "Failed to find all subjects by class section id",
        error,
      );
      throw error;
    }
  }

  public async findById(
    id: SectionSubject["id"],
    options?: ServiceOptions,
  ): Promise<SectionSubjectAssignmentResponse | undefined> {
    try {
      return await this.selectSectionSubjectsQuery(options)
        .where("sectionSubject.id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find section subject by id", error);
      throw error;
    }
  }

  public async verifySectionSubjectAssignmentExists(
    id: SectionSubject["id"],
    options?: ServiceOptions,
  ) {
    let existingSectionSubject: SectionSubjectAssignmentResponse | undefined;
    try {
      existingSectionSubject = await this.findById(id, options);
    } catch (error: unknown) {
      this.logger.error(
        "Failed to verify section subject assignment exists",
        error,
      );
      throw error;
    }

    if (!existingSectionSubject) {
      throw new NotFoundException(
        `Section subject assignment with id: ${id} not found`,
      );
    }
  }

  private async verifyForeignResourcesExist(
    sectionSubjectPayload: Partial<NewSectionSubject>,
    options?: ServiceOptions,
  ) {
    try {
      if (sectionSubjectPayload.academicSessionId) {
        await this.academicSessionsService.verifyAcademicSessionExists(
          sectionSubjectPayload.academicSessionId,
        );
      }

      if (sectionSubjectPayload.classSectionId) {
        await this.classSectionsService.verifyClassSectionExists(
          sectionSubjectPayload.classSectionId,
        );
      }

      if (sectionSubjectPayload.subjectId) {
        await this.subjectsService.verifySubjectExists(
          sectionSubjectPayload.subjectId,
        );
      }

      if (sectionSubjectPayload.subjectTeacherId) {
        const existingSubjectTeacher =
          await this.staffService.findInstitutionalStaffById(
            sectionSubjectPayload.subjectTeacherId,
            options,
          );
        if (!existingSubjectTeacher) {
          throw new NotFoundException(
            `Subject Teacher with id: ${sectionSubjectPayload.subjectTeacherId} not found`,
          );
        }
      }
    } catch (error: unknown) {
      this.logger.error("Failed to verify foreign resources exist", error);
      throw error;
    }
  }

  private selectSectionSubjectsQuery(options?: ServiceOptions) {
    const kyselyClient = options?.trx ?? this.db;
    return kyselyClient
      .selectFrom("sectionSubject")
      .innerJoin("subject", "subject.id", "sectionSubject.subjectId")
      .innerJoin(
        "classSection",
        "classSection.id",
        "sectionSubject.classSectionId",
      )
      .innerJoin("class", "class.id", "classSection.classId")
      .innerJoin("staff", "staff.id", "sectionSubject.subjectTeacherId")
      .innerJoin("users", "staff.userId", "users.id")
      .select([
        "sectionSubject.id as id",
        "sectionSubject.academicSessionId as academicSessionId",
        sql<SectionSubjectAssignmentResponse["class"]>`json_build_object(
            'id', class.id,
            'name', class.name
          )`.as("class"),
        sql<SectionSubjectAssignmentResponse["section"]>`json_build_object(
            'id', class_section.id,
            'name', class_section.name
          )`.as("section"),
        sql<SectionSubjectAssignmentResponse["subject"]>`json_build_object(
            'id', subject.id,
            'name', subject.name
          )`.as("subject"),
        sql<
          SectionSubjectAssignmentResponse["subjectTeacher"]
        >`json_build_object(
            'id', users.id,
            'name', users.name
          )`.as("subjectTeacher"),
        "sectionSubject.createdAt as createdAt",
      ]);
  }
}
