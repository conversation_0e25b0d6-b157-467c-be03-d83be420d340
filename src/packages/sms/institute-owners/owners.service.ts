import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely } from "kysely";
import { UsersService } from "../../../core/users/users.service.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { PasswordService } from "../../../shared/services/password.service.js";
import { EmailService } from "../../../shared/services/email.service.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/common.types.js";
import {
  CreateOwnerPayload,
  InstituteOwner,
  InstituteOwnerProfile,
  InstituteOwnerUpdate,
  UniqueOwnerCriteria,
} from "./types/owner.types.js";
import { logger } from "../../../lib/logger.js";
import { DB } from "../../../database/types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { ModuleRef } from "@nestjs/core";

@Injectable()
export class OwnersService implements OnModuleInit {
  private readonly logger = new Logger(OwnersService.name);
  private usersService: UsersService;

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly rolesService: RolesService,
    private readonly passwordService: PasswordService,
    private readonly emailService: EmailService,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.usersService = this.moduleRef.get(UsersService, { strict: false });
  }

  public async findAll({
    limit,
    offset,
  }: ListAllEntitiesQueryOptions): Promise<Array<InstituteOwnerProfile>> {
    try {
      return await this.findOwnerQuery().offset(offset).limit(limit).execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all owners", error);
      throw error;
    }
  }

  public async update(
    id: InstituteOwner["userId"],
    data: InstituteOwnerUpdate,
  ) {
    await this.verifyOwnerExists(id);

    try {
      await this.db
        .updateTable("instituteOwner")
        .set(data)
        .where("userId", "=", id)
        .returningAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to update owner", error);
      throw error;
    }
  }

  public async findUniqueOwner(
    criteria: Partial<Pick<InstituteOwner, "userId">>,
  ): Promise<InstituteOwnerProfile | undefined> {
    try {
      return await this.findOwnerQuery(criteria).executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find unique owner", error);
      throw error;
    }
  }

  /**
   * Creates a new owner and sends email with temporary credentials
   * @returns Newly create owner profile
   */
  public async create(
    createOwnerInput: CreateOwnerPayload,
  ): Promise<InstituteOwnerProfile> {
    try {
      const randomTempPassword = this.passwordService.generateRandomPassword();

      const newOwner = await this.db.transaction().execute(async trx => {
        const instituteOwnerRole = await this.rolesService.findByNameOrCreate(
          "INSTITUTE_OWNER",
          trx,
        );

        // create new user
        const newUser = await this.usersService.create(
          {
            ...createOwnerInput,
            roleId: instituteOwnerRole.id,
            password: randomTempPassword,
            isPasswordTemporary: true,
          },
          trx,
        );

        // create new owner
        const newOwner = await trx
          .insertInto("instituteOwner")
          .values({
            userId: newUser.id,
          })
          .returningAll()
          .executeTakeFirstOrThrow();

        logger.warn(
          `New owner email: ${createOwnerInput.email} and password: ${randomTempPassword}`,
        );

        return {
          ...newUser,
          hasCompletedSetup: newOwner.hasCompletedSetup,
        };
      });

      void this.emailService.sendEmailWithTempCredentials({
        email: newOwner.email,
        password: randomTempPassword,
      });

      return newOwner;
    } catch (error: unknown) {
      this.logger.error("Failed to create owner", error);
      handleDatabaseInsertException(error, {
        resource: "instituteOwner",
        logger: this.logger,
      });
    }
  }

  private async verifyOwnerExists(userId: string) {
    let existingOwner: InstituteOwnerProfile | undefined;
    try {
      existingOwner = await this.findUniqueOwner({ userId });
    } catch (error: unknown) {
      this.logger.error("Failed to verify owner exists", error);
      throw error;
    }

    if (!existingOwner) {
      throw new NotFoundException(
        `Institute Owner with id: ${userId} not found`,
      );
    }
  }

  private findOwnerQuery(criteria?: UniqueOwnerCriteria) {
    let query = this.db
      .selectFrom("instituteOwner")
      .innerJoin("users", "users.id", "instituteOwner.userId")
      .innerJoin("role", "users.roleId", "role.id")
      .select([
        "users.id as id",
        "role.code as role",
        "users.email as email",
        "users.name as name",
        "users.gender as gender",
        "users.address as address",
        "users.phone as phone",
        "users.cnic as cnic",
        "users.isActive as isActive",
        "users.photo as photo",
        "users.isPasswordTemporary as isPasswordTemporary",
        "instituteOwner.createdAt",
        "instituteOwner.hasCompletedSetup",
      ]);

    if (criteria?.email) {
      query = query.where("users.email", "=", criteria.email);
    }

    if (criteria?.cnic) {
      query = query.where("users.cnic", "=", criteria.cnic);
    }

    if (criteria?.phone) {
      query = query.where("users.phone", "=", criteria.phone);
    }
    if (criteria?.userId) {
      query = query.where("instituteOwner.userId", "=", criteria.userId);
    }

    if (criteria?.createdAt) {
      query = query.where("instituteOwner.createdAt", "=", criteria.createdAt);
    }

    return query;
  }
}
