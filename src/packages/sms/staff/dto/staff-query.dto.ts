import { createZodDto } from "nestjs-zod";
import { listAllEntitiesQuerySchema } from "../../../../shared/schema/zod-common.schema.js";
import { staffRoleSchema } from "./staff.dto.js";

// ------------------- Staff-Query-Dto ------------------->
export const listStaffQuerySchema = listAllEntitiesQuerySchema.extend({
  role: staffRoleSchema.optional(),
});

export class ListStaffQueryDto extends createZodDto(listStaffQuerySchema) {}
