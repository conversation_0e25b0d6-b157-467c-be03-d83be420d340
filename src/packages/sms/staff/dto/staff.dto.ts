import { z } from "zod";
import {
  addressSchema,
  cnicSchema,
  genderSchema,
  imageUrlSchema,
  nameSchema,
  passwordSchema,
  phoneNumberSchema,
  safePositiveNumberSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

export const staffRoleSchema = z.enum(
  ["TEACHER", "BRANCH_ADMIN", "ACCOUNTANT", "SUPPORT_STAFF"],
  {
    message: "Role must be one of 'TEACHER', 'BRANCH_ADMIN', or 'ACCOUNTANT'.",
  },
);

const baseStaffSchema = z.object({
  name: nameSchema,
  email: z
    .string()
    .email({ message: "Invalid email address" })
    .max(100, { message: "Email must be less than 100 characters" }),
  phone: phoneNumberSchema,
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.optional().nullable(),
  password: passwordSchema.optional().nullable(),
  designation: z
    .string()
    .nonempty()
    .max(100, { message: "Designation must be less than 100 characters" }),
  department: z.enum(["ACADEMIC", "ADMINISTRATION", "SUPPORT"], {
    message:
      "Department must be one of 'ACADEMIC', 'ADMINISTRATION', or 'SUPPORT'.",
  }),
  role: staffRoleSchema,
  salary: safePositiveNumberSchema,
  cnic: cnicSchema,
});

// -------------------- Create-Staff-Dto -------------------->
export const createStaffSchema = baseStaffSchema
  .refine(
    data => {
      // Check if the role is valid for the department
      if (data.department === "ACADEMIC" && data.role !== "TEACHER") {
        return false;
      }

      if (
        data.department === "ADMINISTRATION" &&
        data.role !== "BRANCH_ADMIN" &&
        data.role !== "ACCOUNTANT"
      ) {
        return false;
      }

      if (data.department === "SUPPORT" && data.role !== "SUPPORT_STAFF") {
        return false;
      }

      return true;
    },
    {
      message: "Invalid role for the selected department.",
      path: ["role"],
    },
  )
  .refine(
    data => {
      if (data.department !== "SUPPORT" && !data.password) {
        return false;
      }
      return true;
    },
    {
      message: "Password is required for non-support staff.",
      path: ["password"],
    },
  );

export class CreateStaffDto extends createZodDto(createStaffSchema) {}

// -------------------- Update-Staff-Dto -------------------->
export const updateStaffSchema = baseStaffSchema
  .omit({
    department: true,
    role: true,
    email: true,
  })
  .partial();
export class UpdateStaffDto extends createZodDto(updateStaffSchema) {}

// -------------------- Get-Staff-Dto -------------------->
export const staffProfileSchema = z.object({
  id: z.string(),
  ...baseStaffSchema.omit({ password: true }).shape,
  createdAt: z.date(),
});

export class StaffProfileResponseDto extends createZodDto(staffProfileSchema) {}
