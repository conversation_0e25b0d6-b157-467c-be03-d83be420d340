import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { DB } from "../../../database/types.js";
import { Kysely } from "kysely";
import {
  CreateStaffPayload,
  ListStaffQueryOptions,
  Staff,
  StaffProfile,
  UpdateStaffPayload,
} from "./types/staff.types.js";
import {
  extractKeysFromObject,
  omitKeysFromObject,
} from "../../../utils/object-utils.js";
import { UsersService } from "../../../core/users/users.service.js";
import { PasswordService } from "../../../shared/services/password.service.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { EmailService } from "../../../shared/services/email.service.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { Branch } from "../branches/types/branches.types.js";
import { BranchesService } from "../branches/branches.service.js";
import { logger } from "../../../lib/logger.js";
import { ModuleRef } from "@nestjs/core";
import {
  FindAllEntitiesResponse,
  ServiceOptions,
} from "../../../shared/types/common.types.js";

@Injectable()
export class StaffService implements OnModuleInit {
  private readonly logger = new Logger(StaffService.name);

  private userService: UsersService;

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly branchesService: BranchesService,
    private readonly passwordService: PasswordService,
    private readonly rolesService: RolesService,
    private readonly emailService: EmailService,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.userService = this.moduleRef.get(UsersService, {
      strict: false,
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Find All Staff By Branch ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves all staff members associated with a specific
  | branch. It combines both institutional and support staff into a unified
  | result set using a UNION ALL query. The method supports pagination
  | through limit and offset parameters and returns staff sorted by
  | creation date.
  |
  */
  public async findAllByBranchId(
    branchId: string,
    queryOptions: ListStaffQueryOptions,
  ): Promise<FindAllEntitiesResponse<StaffProfile>> {
    try {
      const unifiedStaffQuery = this.selectInstitutionalStaffQuery()
        .where("staff.branchId", "=", branchId)
        .unionAll(
          this.selectSupportStaffQuery().where("staff.branchId", "=", branchId),
        );

      let query = this.db
        .with("unified_staff", _db => unifiedStaffQuery)
        .selectFrom("unified_staff")
        .selectAll()
        .limit(queryOptions.limit)
        .offset(queryOptions.offset)
        .orderBy("createdAt", "asc");

      if (queryOptions.role) {
        query = query.where("role", "=", queryOptions.role);
      }

      const [items, { total }] = await Promise.all([
        query.execute(),
        this.db
          .selectFrom("staff")
          .select(({ fn }) => [fn.count("staff.id").as("total")])
          .where("branchId", "=", branchId)
          .executeTakeFirstOrThrow(),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Staff
  |--------------------------------------------------------------------------
  |
  | This method handles updating existing staff members in the system.
  | It determines the staff type (support or institutional) and delegates
  | the update operation to the appropriate specialized method. The process
  | includes validating the branch, finding the existing staff record,
  | and updating the relevant database tables within a transaction.
  |
  */
  public async update(
    id: Staff["id"],
    data: UpdateStaffPayload,
  ): Promise<StaffProfile> {
    await this.verifyBranchExists(data.branchId);

    const existingStaff = await this.findExistingStaffOrThrow(id);

    try {
      const staffInfo = extractKeysFromObject(data, ["salary", "designation"]);
      const userInfo = omitKeysFromObject(data, [
        "salary",
        "designation",
        "branchId",
      ]);

      if (existingStaff.department === "SUPPORT") {
        return await this.updateSupportStaff(
          id,
          existingStaff,
          staffInfo,
          userInfo,
        );
      }

      return await this.updateInstitutionalStaff(
        id,
        existingStaff,
        staffInfo,
        userInfo,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating support staff members.
  | It updates both the staff table for employment details (salary, designation)
  | and the supportStaffProfile table for personal information within a
  | single database transaction to ensure data consistency.
  |
  */
  private async updateSupportStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    userInfo: Record<string, unknown>,
  ): Promise<StaffProfile> {
    try {
      return await this.db.transaction().execute(async trx => {
        const updatedSupportStaff = await trx
          .updateTable("supportStaffProfile")
          .set(userInfo)
          .where("id", "=", existingStaff.supportStaffProfileId)
          .returningAll()
          .executeTakeFirstOrThrow();

        const updatedStaff = await trx
          .updateTable("staff")
          .set(staffInfo)
          .where("id", "=", id)
          .returningAll()
          .executeTakeFirstOrThrow();

        return {
          ...updatedStaff,
          ...updatedSupportStaff,
        };
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update support staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating institutional staff members
  | (teachers, administrators, accountants). It updates both the staff table
  | for employment details and the users table for personal information
  | within a single database transaction to ensure data consistency.
  |
  */
  private async updateInstitutionalStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    userInfo: Record<string, unknown>,
  ): Promise<StaffProfile> {
    try {
      return await this.db.transaction().execute(async trx => {
        const updatedStaff = await trx
          .updateTable("staff")
          .set(staffInfo)
          .where("id", "=", id)
          .returningAll()
          .executeTakeFirstOrThrow();

        const updatedUser = await trx
          .updateTable("users")
          .set(userInfo)
          .where("id", "=", existingStaff.userId)
          .returning([
            "name",
            "email",
            "phone",
            "address",
            "gender",
            "photo",
            "cnic",
          ])
          .executeTakeFirstOrThrow();

        return {
          ...updatedStaff,
          ...updatedUser,
        };
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update institutional staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create new Staff
  |--------------------------------------------------------------------------
  |
  | This method handles the creation of new staff members for.
  | It supports creating both institutional staff
  | (teachers, administrators, accountants) and support staff. The process
  | includes creating a user account, assigning appropriate roles, generating
  | temporary credentials, and sending login information via email.
  |
  */
  public async create(
    createStaffInput: CreateStaffPayload,
  ): Promise<StaffProfile> {
    await this.verifyBranchExists(createStaffInput.branchId);

    try {
      if (createStaffInput.department === "SUPPORT") {
        return await this.createSupportStaff(createStaffInput);
      }

      const newStaff = await this.createInstitutionalStaff(createStaffInput);

      void this.emailService.sendEmailWithTempCredentials({
        email: newStaff.email,
        password: newStaff.password,
      });
      return newStaff;
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "Staff",
        logger: this.logger,
      });
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of institutional staff members
  | (teachers, administrators, accountants). It creates a user account with
  | the appropriate role, generates a random password, and creates a staff
  | record linked to the user. All operations are performed within a
  | transaction to ensure data consistency.
  |
  */
  private async createInstitutionalStaff(createStaffInput: CreateStaffPayload) {
    try {
      return await this.db.transaction().execute(async trx => {
        const userInfo = this.extractUserProperties(createStaffInput);

        const randomPassword = this.passwordService.generateRandomPassword();

        const staffRole = await this.rolesService.findByNameOrCreate(
          createStaffInput.role,
          trx,
        );

        const newUser = await this.userService.create(
          {
            ...userInfo,
            roleId: staffRole.id,
            password: randomPassword,
          },
          trx,
        );

        const staffInfo = this.extractStaffProperties(createStaffInput);

        const newStaff = await trx
          .insertInto("staff")
          .values({
            id: newUser.id,
            userId: newUser.id,
            ...staffInfo,
          })
          .returningAll()
          .executeTakeFirstOrThrow();

        logger.warn(
          `New staff email: ${newUser.email} and password: ${randomPassword}`,
        );
        return {
          ...newStaff,
          ...userInfo,
          password: randomPassword,
        };
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Institutional Staff By ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves an institutional staff member by their ID.
  | It uses the selectInstitutionalStaffQuery to join the staff and users
  | tables, providing a complete profile of the staff member including
  | both personal and employment information.
  |
  */
  public async findInstitutionalStaffById(
    id: Staff["id"],
    options?: ServiceOptions,
  ): Promise<StaffProfile | undefined> {
    try {
      return await this.selectInstitutionalStaffQuery(options)
        .where("staff.userId", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find staff by id", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Existing Staff Or Throw
  |--------------------------------------------------------------------------
  |
  | This private method attempts to find a staff member by ID and throws
  | a NotFoundException if the staff member doesn't exist. It's used to
  | validate staff existence before performing operations that require
  | a valid staff record.
  |
  */
  private async findExistingStaffOrThrow(id: Staff["id"]): Promise<Staff> {
    let existingStaff: Staff | undefined;

    try {
      existingStaff = await this.db
        .selectFrom("staff")
        .where("id", "=", id)
        .selectAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find existing staff", error);
      throw error;
    }

    if (!existingStaff) {
      throw new NotFoundException(`Staff with id: ${id} not found`);
    }
    return existingStaff;
  }

  /*
  |--------------------------------------------------------------------------
  | Create Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of support staff members.
  | Unlike institutional staff, support staff don't have user accounts
  | but instead have profiles in the supportStaffProfile table. The method
  | creates both the profile and the staff record within a transaction.
  |
  */
  private async createSupportStaff(createStaffInput: CreateStaffPayload) {
    try {
      return await this.db.transaction().execute(async trx => {
        const createSupportStaffProfileInput =
          this.extractUserProperties(createStaffInput);

        const createdSupportStaffProfile = await trx
          .insertInto("supportStaffProfile")
          .values(createSupportStaffProfileInput)
          .returning([
            "id",
            "name",
            "email",
            "phone",
            "address",
            "cnic",
            "photo",
            "gender",
          ])
          .executeTakeFirstOrThrow();

        const staffInfo = this.extractStaffProperties(createStaffInput);

        const createdStaff = await trx
          .insertInto("staff")
          .values({
            ...staffInfo,
            supportStaffProfileId: createdSupportStaffProfile.id,
          })
          .returning(["id", "department", "designation", "salary", "createdAt"])
          .executeTakeFirstOrThrow();

        return {
          ...createdStaff,
          ...createdSupportStaffProfile,
          role: createStaffInput.role,
        };
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Verify Branch Exists
  |--------------------------------------------------------------------------
  |
  | This private method verifies that a branch with the given ID exists
  | before proceeding with staff operations. It throws a NotFoundException
  | if the branch doesn't exist, ensuring that staff are only created or
  | updated for valid branches.
  |
  */
  private async verifyBranchExists(branchId: string) {
    let existingBranch: Branch | undefined;
    try {
      existingBranch = await this.branchesService.findById(branchId);
    } catch (error) {
      this.logger.error("Failed to verify branch exists", error);
      throw error;
    }

    if (!existingBranch) {
      throw new NotFoundException(`Branch with id: ${branchId} not found`);
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Select Support Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve support staff data by
  | joining the supportStaffProfile and staff tables. It selects all
  | necessary fields from both tables and aliases them appropriately
  | to create a unified staff profile view.
  |
  */
  private selectSupportStaffQuery() {
    return this.db
      .selectFrom("supportStaffProfile")
      .innerJoin(
        "staff",
        "staff.supportStaffProfileId",
        "supportStaffProfile.id",
      )
      .select([
        "supportStaffProfile.name as name",
        "supportStaffProfile.email as email",
        "supportStaffProfile.phone as phone",
        "supportStaffProfile.address as address",
        "supportStaffProfile.gender as gender",
        "supportStaffProfile.photo as photo",
        "supportStaffProfile.cnic as cnic",
        "staff.id as id",
        "staff.department as department",
        "staff.designation as designation",
        "staff.salary as salary",
        "staff.createdAt as createdAt",
        "staff.role as role",
      ]);
  }

  /*
  |--------------------------------------------------------------------------
  | Select Institutional Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve institutional staff data
  | by joining the staff and users tables. It selects all necessary fields
  | from both tables and aliases them appropriately to create a unified
  | staff profile view for teachers, administrators, and accountants.
  |
  */
  private selectInstitutionalStaffQuery(options?: ServiceOptions) {
    const kyselyClient = options?.trx ?? this.db;
    return kyselyClient
      .selectFrom("staff")
      .innerJoin("users", "users.id", "staff.userId")
      .select([
        "users.name as name",
        "users.email as email",
        "users.phone as phone",
        "users.address as address",
        "users.gender as gender",
        "users.photo as photo",
        "users.cnic as cnic",
        "staff.id as id",
        "staff.department as department",
        "staff.designation as designation",
        "staff.salary as salary",
        "staff.createdAt as createdAt",
        "staff.role as role",
      ]);
  }

  /*
  |--------------------------------------------------------------------------
  | Extract User Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts personal information fields from
  | the staff input payload. These fields are used to create or update
  | user profiles for institutional staff or support staff profiles.
  |
  */
  private extractUserProperties(createStaffInput: CreateStaffPayload) {
    return extractKeysFromObject(createStaffInput, [
      "name",
      "email",
      "phone",
      "address",
      "gender",
      "photo",
      "cnic",
    ]);
  }

  /*
  |--------------------------------------------------------------------------
  | Extract Staff Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts employment-related fields from
  | the staff input payload. These fields are used to create or update
  | staff records in the staff table, containing information about
  | the staff member's role, department, salary, and designation.
  |
  */
  private extractStaffProperties(createStaffInput: CreateStaffPayload) {
    return extractKeysFromObject(createStaffInput, [
      "salary",
      "department",
      "designation",
      "branchId",
      "role",
    ]);
  }
}
