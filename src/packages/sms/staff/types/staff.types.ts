import { Insertable, Selectable, Updateable } from "kysely";
import { StaffTable } from "../../../../database/types.js";
import {
  createStaffSchema,
  staffProfileSchema,
  updateStaffSchema,
} from "../dto/staff.dto.js";
import { z } from "zod";
import { listStaffQuerySchema } from "../dto/staff-query.dto.js";

export type NewStaff = Insertable<StaffTable>;
export type Staff = Selectable<StaffTable>;
export type StaffUPdate = Updateable<StaffTable>;

export type CreateStaffPayload = z.infer<typeof createStaffSchema> & {
  branchId: string;
};

export type UpdateStaffPayload = z.infer<typeof updateStaffSchema> & {
  branchId: string;
};

export type StaffProfile = z.infer<typeof staffProfileSchema>;

// Query
export type ListStaffQueryOptions = z.infer<typeof listStaffQuerySchema>;
