import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StaffService } from "./staff.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import {
  CreateStaffParamDto,
  UpdateStaffParamDto,
} from "./dto/staff-param.dto.js";
import { CreateStaffDto, UpdateStaffDto } from "./dto/staff.dto.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { ApiDocCreateStaff, ApiDocGetAllStaff } from "./docs/staff.docs.js";

@Controller("sms/branches")
@ApiTags("Staff")
export class StaffController {
  public constructor(private readonly staffService: StaffService) {}

  @Roles(["INSTITUTE_OWNER"])
  @Post("/:branchId/staff")
  @ApiDocCreateStaff()
  public async create(
    @Param() params: CreateStaffParamDto,
    @Body() createStaffDto: CreateStaffDto,
  ) {
    return await this.staffService.create({
      ...createStaffDto,
      branchId: params.branchId,
    });
  }

  @Roles(["INSTITUTE_OWNER"])
  @Get("/:branchId/staff")
  @ApiDocGetAllStaff()
  public async get(
    @Param() params: CreateStaffParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.staffService.findAllByBranchId(params.branchId, query);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch("/:branchId/staff/:staffId")
  public async update(
    @Param() param: UpdateStaffParamDto,
    @Body() updateStaffDto: UpdateStaffDto,
  ) {
    return this.staffService.update(param.staffId, {
      branchId: param.branchId,
      ...updateStaffDto,
    });
  }
}
