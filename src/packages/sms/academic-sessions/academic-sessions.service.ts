import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { DB } from "../../../database/types.js";
import { Kysely, Transaction, Updateable } from "kysely";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import {
  NewAcademicSession,
  AcademicSession,
} from "./types/academic-session.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { Branch } from "../branches/types/branches.types.js";
import { BranchesService } from "../branches/branches.service.js";

@Injectable()
export class AcademicSessionsService {
  private readonly logger = new Logger(AcademicSessionsService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly branchesService: BranchesService,
  ) {}

  public async create(data: NewAcademicSession): Promise<AcademicSession> {
    await this.verifyBranchExists(data.branchId);

    try {
      return await this.db.transaction().execute(async trx => {
        // deactivate previous session
        await this.deactivateBranchPreviousSessions(data.branchId, trx);
        return await trx
          .insertInto("academicSession")
          .values(data)
          .returningAll()
          .executeTakeFirstOrThrow();
      });
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "academicSession",
        logger: this.logger,
      });
    }
  }

  public async findById(
    id: AcademicSession["id"],
  ): Promise<AcademicSession | undefined> {
    try {
      return await this.db
        .selectFrom("academicSession")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find academic session by id", error);
      throw error;
    }
  }

  public async update(
    criteria: Partial<Pick<AcademicSession, "branchId" | "id" | "isActive">>,
    data: Updateable<AcademicSession>,
    trx?: Transaction<DB>,
  ) {
    const kyselyClient = trx ?? this.db;
    try {
      let query = kyselyClient
        .updateTable("academicSession")
        .set(data)
        .returningAll();

      if (criteria.branchId) {
        query = query.where("branchId", "=", criteria.branchId);
      }

      if (criteria.isActive) {
        query = query.where("isActive", "=", criteria.isActive);
      }

      if (criteria.id) {
        query = query.where("id", "=", criteria.id);
      }

      return await query.executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to update academic session", error);
      throw error;
    }
  }

  public async findAllByBranchId(branchId: string): Promise<AcademicSession[]> {
    try {
      return await this.db
        .selectFrom("academicSession")
        .selectAll()
        .where("branchId", "=", branchId)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to find all academic sessions", error);
      throw error;
    }
  }

  private async verifyBranchExists(branchId: string) {
    let existingBranch: Branch | undefined;
    try {
      existingBranch = await this.branchesService.findById(branchId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify branch exists", error);
      throw error;
    }

    if (!existingBranch) {
      throw new NotFoundException(`Branch with id: ${branchId} not found`);
    }
  }

  private async deactivateBranchPreviousSessions(
    branchId: AcademicSession["branchId"],
    trx: Transaction<DB>,
  ) {
    try {
      await this.update({ branchId, isActive: true }, { isActive: false }, trx);
    } catch (error: unknown) {
      this.logger.error(`Failed to deactivate branch previous sessions`, error);
      throw error;
    }
  }

  public async verifyAcademicSessionExists(
    academicSessionId: AcademicSession["id"],
  ) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession = await this.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }
}
