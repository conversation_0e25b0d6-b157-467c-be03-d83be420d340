import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/common.types.js";

import { OwnersService } from "../institute-owners/owners.service.js";
import {
  FindOwnerInstituteResponse,
  InstituteResponse,
  Institute,
  InstituteUpdate,
  NewInstitute,
} from "./types/institute.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";

@Injectable()
export class InstitutesService {
  private readonly logger = new Logger(InstitutesService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly ownersService: OwnersService,
  ) {}

  public async findAll({
    limit,
    offset,
  }: ListAllEntitiesQueryOptions): Promise<InstituteResponse[]> {
    try {
      return await this.findInstituteQuery()
        .limit(limit)
        .offset(offset)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all institutes", error);
      throw error;
    }
  }

  public async findOwnerInstitute(
    ownerId: Institute["ownerId"],
  ): Promise<FindOwnerInstituteResponse> {
    try {
      const existingInstitute = await this.findInstituteQuery({ ownerId })
        .orderBy("createdAt", "asc")
        .executeTakeFirst();

      if (existingInstitute) {
        return {
          institute: existingInstitute,
        };
      }

      return { institute: null };
    } catch (error: unknown) {
      this.logger.error("Failed to get owner institute", error);
      throw error;
    }
  }

  public async updateOwnerInstitute(
    ownerId: Institute["ownerId"],
    payload: Omit<InstituteUpdate, "ownerId" | "createdAt">,
  ) {
    const { id: instituteId } = await this.findOwnerInstituteOrThrow(ownerId);

    try {
      return await this.update(instituteId, payload);
    } catch (error: unknown) {
      this.logger.error("Failed to update owner institute", error);
      throw error;
    }
  }

  public async findById(
    id: Institute["id"],
  ): Promise<InstituteResponse | undefined> {
    try {
      return await this.findInstituteQuery({
        id,
      }).executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error(`Failed to find institute by id: ${id}`, error);
      throw error;
    }
  }

  public async create(
    data: Omit<NewInstitute, "ownerId">,
    userId: string,
  ): Promise<InstituteResponse> {
    const existingOwner = await this.findOwnerOrThrow(userId);
    try {
      return await this.db
        .insertInto("institute")
        .values({
          ...data,
          ownerId: existingOwner.id,
        })
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create institute", error);
      handleDatabaseInsertException(error, {
        resource: "institute",
        logger: this.logger,
      });
    }
  }

  public async update(
    id: Institute["id"],
    data: Omit<InstituteUpdate, "ownerId" | "createdAt">,
  ): Promise<InstituteResponse> {
    try {
      return await this.db
        .updateTable("institute")
        .set(data)
        .where("id", "=", id)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to update institute", error);
      throw error;
    }
  }

  private async findOwnerInstituteOrThrow(ownerId: Institute["ownerId"]) {
    let institute: InstituteResponse | null;
    try {
      const res = await this.findOwnerInstitute(ownerId);
      institute = res.institute;
    } catch (error: unknown) {
      this.logger.error("Failed to verify owner institute exists", error);
      throw error;
    }

    if (!institute) {
      throw new NotFoundException("Owner institute not found");
    }
    return institute;
  }

  private async findOwnerOrThrow(userId: string) {
    let owner;

    try {
      owner = await this.ownersService.findUniqueOwner({ userId });
    } catch (error: unknown) {
      this.logger.error("Failed to check if owner exists", error);
      throw error;
    }

    if (!owner) {
      throw new NotFoundException(`Owner with doesn't  exist`);
    }

    return owner;
  }

  private findInstituteQuery(
    criteria?: Partial<Pick<Institute, "name" | "id" | "ownerId">>,
  ) {
    let query = this.db.selectFrom("institute").selectAll();
    if (criteria?.name) {
      query = query.where("institute.name", "=", criteria.name);
    }

    if (criteria?.id) {
      query = query.where("institute.id", "=", criteria.id);
    }

    if (criteria?.ownerId) {
      query = query.where("institute.ownerId", "=", criteria.ownerId);
    }

    return query;
  }
}
