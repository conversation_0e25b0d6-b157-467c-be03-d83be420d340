import { Body, Controller, Param, Post } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StudentsService } from "./students.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { CreateStudentDto } from "./dto/students.dto.js";
import { StudentsParamsDto } from "./dto/students-params.dto.js";

@Controller("sms/sessions")
@ApiTags("Students")
export class StudentsController {
  public constructor(private readonly studentsService: StudentsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post(":academicSessionId/students")
  public create(
    @Body() createStudentDto: CreateStudentDto,
    @Param() param: StudentsParamsDto,
  ) {
    return this.studentsService.create({
      ...createStudentDto,
      academicSessionId: param.academicSessionId,
    });
  }
}
