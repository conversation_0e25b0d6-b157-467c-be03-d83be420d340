import { z } from "zod";
import {
  addressSchema,
  cnicSchema,
  dateSchema,
  genderSchema,
  getUuidSchema,
  imageUrlSchema,
  nameSchema,
  phoneNumberSchema,
} from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

// ------------------- Student-Base-Schema ------------------->
const studentBaseSchema = z.object({
  name: nameSchema,
  registrationNumber: z
    .string()
    .nonempty()
    .max(100, {
      message: "Registration number must be less than 100 characters",
    })
    .optional(),
  email: z
    .string()
    .email({ message: "Invalid email address" })
    .max(100, { message: "Email must be less than 100 characters" }),
  address: addressSchema,
  gender: genderSchema,
  photo: imageUrlSchema.optional(),
  monthlyFee: z.number().positive().max(1_000_000, {
    message: "Monthly fee cannot exceed 1,000,000",
  }),
  admissionDate: dateSchema,
  dateOfBirth: dateSchema,
  guardianName: nameSchema,
  guardianPhone: phoneNumberSchema,
  guardianEmail: z.string().email().max(100, {
    message: "Email must be less than 100 characters",
  }),
  guardianAddress: addressSchema,
  guardianGender: genderSchema,
  guardianRelation: z.enum(["FATHER", "MOTHER", "GUARDIAN"], {
    message:
      "Guardian relation must be one of 'FATHER', 'MOTHER', or 'GUARDIAN'.",
  }),
  guardianCnic: cnicSchema,
  classSectionId: getUuidSchema("Class Section ID"),
});

// ------------------- Create-Student-Dto ------------------->
export const createStudentSchema = studentBaseSchema;
export class CreateStudentDto extends createZodDto(studentBaseSchema) {}
