import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely, Transaction } from "kysely";
import { DB } from "../../../database/types.js";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { CreateStudentPayload, Student } from "./types/students.types.js";
import { GuardiansService } from "../guardians/guardians.service.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { AcademicSession } from "../academic-sessions/types/academic-session.types.js";
import { extractKeysFromObject } from "../../../utils/object-utils.js";
import { ClassSection } from "../classes/sections/types/class-sections.types.js";
import { ClassSectionsService } from "../classes/sections/class-sections.service.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { EnrollmentsService } from "../enrollments/enrollments.service.js";
import { EmailService } from "../../../shared/services/email.service.js";

@Injectable()
export class StudentsService {
  private readonly logger = new Logger(StudentsService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly academicSessionsService: AcademicSessionsService,
    private readonly guardiansService: GuardiansService,
    private readonly classSectionsService: ClassSectionsService,
    private readonly enrollmentsService: EnrollmentsService,
    private readonly emailService: EmailService,
  ) {}

  public async create(createStudentPayload: CreateStudentPayload) {
    await this.verifyAcademicSessionExists(
      createStudentPayload.academicSessionId,
    );

    await this.verifyClassSectionExists(createStudentPayload.classSectionId);

    try {
      const guardianInfo = this.extractGuardianInfo(createStudentPayload);

      const newStudent = await this.db.transaction().execute(async trx => {
        const guardian = await this.guardiansService.create(guardianInfo, trx);

        const studentInfo = this.extractStudentInfo(createStudentPayload);

        const student = await trx
          .insertInto("student")
          .values({
            ...studentInfo,
            guardianId: guardian.id,
          })
          .returningAll()
          .executeTakeFirstOrThrow();

        await this.enrollStudent(student.id, createStudentPayload, trx);

        return { ...student, guardian };
      });

      void this.emailService.sendEmailWithTempCredentials({
        email: newStudent.guardian.email,
        password: newStudent.guardian.tempPassword,
      });

      return extractKeysFromObject(newStudent, ["guardian"]);
    } catch (error: unknown) {
      this.logger.error("Failed to create student");
      handleDatabaseInsertException(error, {
        resource: "student",
        logger: this.logger,
      });
    }
  }

  public async findById(id: Student["id"]) {
    try {
      return await this.db
        .selectFrom("student")
        .selectAll()
        .where("id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find student by id", error);
      throw error;
    }
  }

  private async enrollStudent(
    studentId: Student["id"],
    createStudentPayload: CreateStudentPayload,
    trx: Transaction<DB>,
  ) {
    const enrollmentPayload = {
      studentId,
      classSectionId: createStudentPayload.classSectionId,
      academicSessionId: createStudentPayload.academicSessionId,
      type: "ADMISSION",
      status: "ACTIVE",
      date: createStudentPayload.admissionDate,
    } as const;

    try {
      await this.enrollmentsService.createEnrollmentWithoutVerification(
        enrollmentPayload,
        trx,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to enroll student");
      throw error;
    }
  }

  private async verifyClassSectionExists(classSectionId: ClassSection["id"]) {
    let existingClassSection: ClassSection | undefined;
    try {
      existingClassSection =
        await this.classSectionsService.findById(classSectionId);
    } catch (error: unknown) {
      this.logger.log("Failed to verify class section exists", error);
      throw error;
    }

    if (!existingClassSection) {
      throw new NotFoundException(
        `Class section with id: ${classSectionId} not found`,
      );
    }
  }

  private async verifyAcademicSessionExists(
    academicSessionId: AcademicSession["id"],
  ) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession =
        await this.academicSessionsService.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.log("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }

  private extractStudentInfo(createStudentPayload: CreateStudentPayload) {
    return extractKeysFromObject(createStudentPayload, [
      "name",
      "registrationNumber",
      "email",
      "address",
      "gender",
      "photo",
      "monthlyFee",
      "admissionDate",
      "dateOfBirth",
      "classSectionId",
    ]);
  }

  private extractGuardianInfo(createStudentPayload: CreateStudentPayload) {
    const {
      guardianAddress: address,
      guardianEmail: email,
      guardianName: name,
      guardianPhone: phone,
      guardianRelation: relation,
      guardianCnic: cnic,
      guardianGender: gender,
    } = createStudentPayload;
    return { address, email, gender, name, phone, relation, cnic };
  }
}
