import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { DB } from "../../../../database/types.js";
import { Kysely, Transaction } from "kysely";
import { ListAllEntitiesQueryOptions } from "../../../../shared/types/common.types.js";
import { NewEnrollment } from "./types/enrollment.types.js";
import { handleDatabaseInsertException } from "../../../../utils/pg-utils.js";
import { StudentsService } from "../students.service.js";
import { ClassSectionsService } from "../../classes/sections/class-sections.service.js";
import { AcademicSessionsService } from "../../academic-sessions/academic-sessions.service.js";
import { ClassSection } from "../../classes/sections/types/class-sections.types.js";
import { AcademicSession } from "../../academic-sessions/types/academic-session.types.js";
import { ModuleRef } from "@nestjs/core";

@Injectable()
export class EnrollmentService implements OnModuleInit {
  private readonly logger = new Logger(EnrollmentService.name);
  private studentsService: StudentsService;
  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly moduleRef: ModuleRef,
    private readonly classSectionsService: ClassSectionsService,
    private readonly academicSessionsService: AcademicSessionsService,
  ) {}

  public onModuleInit() {
    this.studentsService = this.moduleRef.get(StudentsService, {
      strict: false,
    });
  }

  public async findAllByAcademicSessionId(
    academicSessionId: string,
    queryOptions: ListAllEntitiesQueryOptions,
  ) {
    try {
      return await this.selectEnrollmentQuery()
        .where("enrollment.academicSessionId", "=", academicSessionId)
        .limit(queryOptions.limit)
        .offset(queryOptions.offset)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to find all session enrollments", error);
      throw error;
    }
  }

  public async create(createEnrollmentPayload: NewEnrollment) {
    await this.verifyStudentExists(createEnrollmentPayload.studentId);
    await this.verifyClassSectionExists(createEnrollmentPayload.classSectionId);
    await this.verifyAcademicSessionExists(
      createEnrollmentPayload.academicSessionId,
    );

    try {
      return await this.db
        .insertInto("enrollment")
        .values(createEnrollmentPayload)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create enrollment", error);
      handleDatabaseInsertException(error, {
        resource: "enrollment",
        logger: this.logger,
      });
    }
  }

  /**
   * Creates an enrollment record without verifying the existence of related entities.
   * Useful when called from other services that have already performed verification.
   *
   * @param createEnrollmentPayload - The enrollment data to be inserted
   * @param trx - Optional transaction object for database operations
   * @returns The created enrollment record
   */
  public async createEnrollmentWithoutVerification(
    createEnrollmentPayload: NewEnrollment,
    trx?: Transaction<DB>,
  ) {
    try {
      const kyselyClient = trx ?? this.db;

      return await kyselyClient
        .insertInto("enrollment")
        .values(createEnrollmentPayload)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create enrollment", error);
      handleDatabaseInsertException(error, {
        resource: "enrollment",
        logger: this.logger,
      });
    }
  }
  private selectEnrollmentQuery() {
    return this.db
      .selectFrom("enrollment")
      .innerJoin("student", "student.id", "enrollment.studentId")
      .innerJoin("classSection", "classSection.id", "enrollment.classSectionId")
      .innerJoin("class", "class.id", "classSection.classId")
      .innerJoin(
        "academicSession",
        "academicSession.id",
        "enrollment.academicSessionId",
      )
      .orderBy("enrollment.createdAt", "asc")
      .select([
        "enrollment.id",
        "enrollment.type",
        "enrollment.status",
        "enrollment.date",
        "enrollment.createdAt",
        "student.name as studentName",
        "class.name as className",
        "classSection.name as classSectionName",
        "academicSession.name as academicSessionName",
      ]);
  }

  private async verifyStudentExists(studentId: string) {
    let existingStudent;
    try {
      existingStudent = await this.studentsService.findById(studentId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify student exists", error);
      throw error;
    }

    if (!existingStudent) {
      throw new NotFoundException(`Student with id: ${studentId} not found`);
    }
  }

  private async verifyClassSectionExists(classSectionId: string) {
    let existingClassSection: ClassSection | undefined;
    try {
      existingClassSection =
        await this.classSectionsService.findById(classSectionId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify class section exists", error);
      throw error;
    }

    if (!existingClassSection) {
      throw new NotFoundException(
        `Class section with id: ${classSectionId} not found`,
      );
    }
  }

  private async verifyAcademicSessionExists(academicSessionId: string) {
    let existingAcademicSession: AcademicSession | undefined;
    try {
      existingAcademicSession =
        await this.academicSessionsService.findById(academicSessionId);
    } catch (error: unknown) {
      this.logger.error("Failed to verify academic session exists", error);
      throw error;
    }

    if (!existingAcademicSession) {
      throw new NotFoundException(
        `Academic session with id: ${academicSessionId} not found`,
      );
    }
  }
}
