import { INestApplication, Logger } from "@nestjs/common";

import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

/**
 * Configure and initialize Swagger/OpenAPI documentation
 * @param app NestJS application instance
 */
export function setupSwaggerDocumentation(app: INestApplication) {
  // Define API metadata and configuration options
  const swaggerConfig = new DocumentBuilder()
    .setTitle("Ebridge API")
    .setDescription(
      `
      Complete API documentation for the Ebridge platform.
      
      This API provides endpoints for managing packages, users, and other core business entities.
      All protected routes require authentication via Bearer token obtained from the auth endpoints.
      
      ## Environment Information
      - **Current Version**: 1.0.0
      - **Base URL**: ${process.env.API_BASE_URL ?? "https://api.ebridge.com"}
      - **Environment**: ${process.env.NODE_ENV ?? "development"}
    `,
    )
    .setVersion("1.0.0")
    .addServer(
      process.env.API_BASE_URL ?? "https://api.ebridge.com",
      "Production Server",
    )
    .addServer("https://staging-api.ebridge.com", "Staging Server")
    .addServer("http://*************:3000", "Local Development")
    .setContact(
      "Ebridge API Support",
      "https://ebridge.com/support",
      "<EMAIL>",
    )
    .setLicense("Proprietary", "https://ebridge.com/terms")
    .addBearerAuth(
      {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        name: "Authorization",
        description: "Enter JWT token",
        in: "header",
      },
      "access-token",
    )
    .addTag("Subscription Plans", "Endpoints for managing subscription plans")
    .addTag("Subscriptions", "Endpoints for managing subscriptions")
    .addTag("Institutes", "Endpoints for managing institutes")
    .addTag("Institute Owners", "Endpoints for managing institute owners")
    .addTag("Institute Branches", "Endpoints for managing institute branches")
    .addTag("Academic Sessions", "Endpoints for managing academic sessions")
    .addTag("Staff", "Endpoints for managing staff")
    .addTag("Classes", "Endpoints for managing classes")
    .addTag("Students", "Endpoints for managing students")
    .addTag("Guardians", "Endpoints for managing guardians")
    .addTag(
      "Health Check",
      "Endpoints for checking the API's health and status",
    )
    .build();

  const swaggerDocument = SwaggerModule.createDocument(app, swaggerConfig, {
    deepScanRoutes: true,
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    extraModels: [], // Add any additional DTOs that need to be included in schema definitions
  });

  // Setup the Swagger UI endpoint
  SwaggerModule.setup("/docs", app, swaggerDocument, {
    explorer: true,
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: "none",
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      deepLinking: true,
      displayOperationId: false,
      displayRequestDuration: true,
      tryItOutEnabled: false,
    },
    customSiteTitle: "Ebridge API Documentation",
    customfavIcon: "https://ebridge.com/favicon.ico",
  });

  Logger.log(
    `Swagger documentation is available at ${process.env.API_BASE_URL ?? "http://localhost:3000"}/docs`,
  );
}
