import { applyDecorators } from "@nestjs/common";
import { Api<PERSON><PERSON>y, ApiResponse, ApiResponseOptions } from "@nestjs/swagger";

type CommonHttpStatusKeys =
  | "BAD_REQUEST"
  | "NOT_FOUND"
  | "CONFLICT"
  | "INTERNAL_SERVER_ERROR";

export const commonApiResponseOptions: Record<
  CommonHttpStatusKeys,
  ApiResponseOptions
> = {
  BAD_REQUEST: {
    status: 400,
    description: "Bad Request - Invalid data provided",
    schema: {
      type: "object",
      properties: {
        statusCode: { type: "number", example: 400 },
        message: { type: "string", example: "Validation failed" },
        errors: {
          type: "array",
          items: {
            type: "object",
            properties: {
              field: { type: "string" },
              message: { type: "string" },
            },
          },
          example: [
            { field: "name", message: "Name is required" },
            { field: "email", message: "Email is required" },
          ],
        },
      },
    },
  },
  NOT_FOUND: {
    status: 404,
    description: "Not Found - Record with given input does not exist",
    schema: {
      type: "object",
      properties: {
        statusCode: { type: "number", example: 404 },
        message: { type: "string", example: "Record not found" },
      },
    },
  },
  CONFLICT: {
    status: 409,
    description: "Conflict - Updated details conflict with existing record",
    schema: {
      type: "object",
      properties: {
        statusCode: { type: "number", example: 409 },
        message: {
          type: "string",
          example: "Record with these details already exists",
        },
      },
    },
  },

  INTERNAL_SERVER_ERROR: {
    status: 500,
    description: "Internal Server Error",
    schema: {
      type: "object",
      properties: {
        statusCode: { type: "number", example: 500 },
        message: { type: "string", example: "Internal server error" },
      },
    },
  },
} as const;

export function useCommonCreateResourceApiResponses(
  ...decorators: Array<MethodDecorator | ClassDecorator | PropertyDecorator>
) {
  return applyDecorators(
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.CONFLICT),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
    ...decorators,
  );
}

export function useCommonListResourceDocs(
  ...decorators: Array<MethodDecorator | ClassDecorator | PropertyDecorator>
) {
  return applyDecorators(
    ApiQuery({
      name: "limit",
      required: false,
      description: "Maximum number of items to retrieve",
      type: Number,
      example: 10,
    }),
    ApiQuery({
      name: "offset",
      required: false,
      description: "Number of items to skip",
      type: Number,
      example: 1,
    }),
    ...decorators,
  );
}
