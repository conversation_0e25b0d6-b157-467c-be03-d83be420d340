import { Controller, Get, Req } from "@nestjs/common";
import { UsersService } from "./users.service.js";
import { ApiTags } from "@nestjs/swagger";
import type { Request } from "express";

@Controller()
@ApiTags("Users")
export class UsersController {
  public constructor(private readonly usersService: UsersService) {}

  @Get(["sms/users/me", "platform/users/me"])
  public async getCurrentUserProfile(@Req() req: Request) {
    return await this.usersService.getProfile(req.user.sub, req.user.role);
  }
}
