import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  addressSchema,
  cnicSchema,
  genderSchema,
  getUuidSchema,
  imageUrlSchema,
  phoneNumberSchema,
} from "../../../shared/schema/zod-common.schema.js";

export const baseUserSchema = z.object({
  name: z.string().max(100, { message: "Name cannot exceed 100 characters" }),
  email: z
    .string({ message: "Email is required" })
    .email({ message: "Email is invalid" }),
  phone: phoneNumberSchema,
  address: addressSchema,
  gender: genderSchema,
  cnic: cnicSchema,
  photo: imageUrlSchema.optional(),
});

// ------------------- Create-User-Dto ------------------->
export const createUserSchema = baseUserSchema.extend({
  password: z
    .string({ message: "Password is required" })
    .min(8, { message: "Password must be at least 8 characters" }),
});

export class CreateUserDto extends createZodDto(createUserSchema) {}

// -------------------- Get-User-Profile-Dto -------------------->
export const userProfileSchema = baseUserSchema.extend({
  id: getUuidSchema("User Id"),
  createdAt: z.coerce.date(),
  role: z.number(),
  isActive: z.boolean(),
  photo: imageUrlSchema.nullable(),
  isPasswordTemporary: z.boolean(),
});

export class UserProfileDto extends createZodDto(userProfileSchema) {}
