import {
  Api<PERSON><PERSON>ation,
  ApiResponse,
  ApiCookie<PERSON>uth,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { useCommonCreateResourceApiResponses } from "../../../docs/shared/api-responses.js";
// Note: Using schema definitions instead of DTO classes for better compatibility
// The actual response types are defined in the service interfaces

/**
 * Swagger documentation for SMS user login endpoint
 * Handles authentication for SMS system users (teachers, students, staff, etc.)
 */
export function ApiDocSmsLogin() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "SMS User Login",
      description: `
        Authenticates SMS system users including teachers, students, staff, guardians,
        institute owners, and branch administrators. Returns an access token and user role.

        **Important Notes:**
        - Users with temporary passwords will only receive an access token (no refresh token)
        - Users must change their temporary password using the set-password endpoint
        - Refresh token is set as an HTTP-only cookie for security
        - Access tokens expire in 15 minutes, refresh tokens in 30 minutes
      `,
    }),
    ApiResponse({
      status: 200,
      description: "Login successful - returns access token and user role",
      schema: {
        type: "object",
        properties: {
          accessToken: {
            type: "string",
            description: "JWT access token for authenticating API requests",
            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          },
          role: {
            type: "string",
            description: "User role code",
            example: "TEACHER",
            enum: [
              "TEACHER",
              "STUDENT",
              "GUARDIAN",
              "INSTITUTE_OWNER",
              "BRANCH_ADMIN",
              "ACCOUNTANT",
              "SUPPORT_STAFF",
            ],
          },
          refreshToken: {
            type: "string",
            description:
              "JWT refresh token (only provided if user doesn't have temporary password)",
            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            nullable: true,
          },
        },
        required: ["accessToken", "role"],
      },
    }),
    ApiResponse({
      status: 401,
      description: "Unauthorized - Invalid credentials or account locked",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 401 },
          message: {
            type: "string",
            example: "Invalid email or password",
          },
        },
      },
    }),
    ApiResponse({
      status: 403,
      description: "Forbidden - User does not have SMS system access",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 403 },
          message: {
            type: "string",
            example:
              "You do not have the required permissions to access this resource",
          },
        },
      },
    }),
  );
}

/**
 * Swagger documentation for Platform admin login endpoint
 * Handles authentication for platform administrators only
 */
export function ApiDocPlatformLogin() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Platform Admin Login",
      description: `
        Authenticates platform administrators who manage the entire system.
        Returns an access token, user role, and sets a refresh token cookie.

        **Access Control:**
        - Only users with PLATFORM_ADMIN role can use this endpoint
        - Always returns a refresh token (no temporary password restrictions)
        - Refresh token is automatically set as an HTTP-only cookie
      `,
    }),
    ApiResponse({
      status: 200,
      description: "Login successful - returns access token and user role",
      schema: {
        type: "object",
        properties: {
          accessToken: {
            type: "string",
            description: "JWT access token for authenticating API requests",
            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          },
          role: {
            type: "string",
            description: "User role code",
            example: "PLATFORM_ADMIN",
            enum: ["PLATFORM_ADMIN"],
          },
        },
        required: ["accessToken", "role"],
      },
    }),
    ApiResponse({
      status: 401,
      description: "Unauthorized - Invalid credentials",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 401 },
          message: {
            type: "string",
            example: "Invalid email or password",
          },
        },
      },
    }),
    ApiResponse({
      status: 403,
      description: "Forbidden - User is not a platform administrator",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 403 },
          message: {
            type: "string",
            example:
              "You do not have the required permissions to access this resource",
          },
        },
      },
    }),
  );
}

/**
 * Swagger documentation for set password endpoint
 * Allows users to change their temporary password to a permanent one
 */
export function ApiDocSetPassword() {
  return useCommonCreateResourceApiResponses(
    ApiBearerAuth("access-token"),
    ApiOperation({
      summary: "Set User Password",
      description: `
        Allows authenticated users to set a new password, typically used to change
        temporary passwords to permanent ones. This endpoint requires authentication.

        **Usage Scenarios:**
        - New users changing their temporary password
        - Users updating their existing password
        - Password reset completion

        **Security Requirements:**
        - Must be authenticated with a valid access token
        - Password must meet complexity requirements (8+ chars, uppercase, lowercase, number, special char)
        - Old temporary passwords are invalidated after successful change
      `,
    }),
    ApiResponse({
      status: 204,
      description: "Password updated successfully - no content returned",
    }),
    ApiResponse({
      status: 401,
      description: "Unauthorized - Invalid or missing access token",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 401 },
          message: {
            type: "string",
            example: "Unauthorized",
          },
        },
      },
    }),
    ApiResponse({
      status: 404,
      description: "Not Found - User account not found",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 404 },
          message: {
            type: "string",
            example: "User not found",
          },
        },
      },
    }),
  );
}

/**
 * Swagger documentation for refresh token endpoint
 * Generates new access tokens using refresh token from cookies
 */
export function ApiDocRefreshToken() {
  return useCommonCreateResourceApiResponses(
    ApiCookieAuth("refresh_token"),
    ApiOperation({
      summary: "Refresh Access Token",
      description: `
        Generates a new access token using the refresh token stored in HTTP-only cookies.
        This endpoint extends user sessions without requiring re-authentication.

        **Token Management:**
        - Refresh token must be present in cookies (automatically sent by browser)
        - Returns a new access token with the same permissions
        - Refresh token remains valid until expiration
        - Access tokens expire in 15 minutes, refresh tokens in 30 minutes

        **Security Features:**
        - Refresh tokens are HTTP-only cookies (not accessible via JavaScript)
        - Automatic token rotation for enhanced security
        - Validates token signature and expiration
      `,
    }),
    ApiResponse({
      status: 200,
      description:
        "Token refresh successful - returns new access token and user role",
      schema: {
        type: "object",
        properties: {
          accessToken: {
            type: "string",
            description: "New JWT access token for authenticating API requests",
            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
          },
          role: {
            type: "string",
            description: "User role code",
            example: "TEACHER",
            enum: [
              "TEACHER",
              "STUDENT",
              "GUARDIAN",
              "INSTITUTE_OWNER",
              "BRANCH_ADMIN",
              "ACCOUNTANT",
              "SUPPORT_STAFF",
              "PLATFORM_ADMIN",
            ],
          },
        },
        required: ["accessToken", "role"],
      },
    }),
    ApiResponse({
      status: 401,
      description: "Unauthorized - Missing, invalid, or expired refresh token",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 401 },
          message: {
            type: "string",
            example: "Invalid or expired refresh token",
          },
        },
      },
    }),
  );
}
