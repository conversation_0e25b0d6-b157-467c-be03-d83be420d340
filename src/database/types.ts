/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type EnrollmentStatus =
  | "ACTIVE"
  | "COMPLETED"
  | "DECEASED"
  | "EXPELLED"
  | "GRADUATED"
  | "WITHDRAWN";

export type EnrollmentType =
  | "ADMISSION"
  | "PROMOTION"
  | "REPEATING"
  | "TRANSFER_IN";

export type Gender = "FEMALE" | "MALE" | "OTHER";

export type Generated<T> =
  T extends ColumnType<infer S, infer I, infer U>
    ? ColumnType<S, I | undefined, U>
    : ColumnType<T, T | undefined, T>;

export type GuardianRelation = "FATHER" | "GUARDIAN" | "MOTHER";

export type Numeric = number;

export type Religion =
  | "BUDDHISM"
  | "CHRISTIANITY"
  | "HINDUISM"
  | "ISLAM"
  | "JUDAISM"
  | "OTHER"
  | "SIKHISM";

export type RoleName =
  | "ACCOUNTANT"
  | "BRANCH_ADMIN"
  | "GUARDIAN"
  | "INSTITUTE_OWNER"
  | "PLATFORM_ADMIN"
  | "STUDENT"
  | "SUPPORT_STAFF"
  | "TEACHER";

export type StaffDepartment = "ACADEMIC" | "ADMINISTRATION" | "SUPPORT";

export type StaffRole =
  | "ACCOUNTANT"
  | "BRANCH_ADMIN"
  | "SUPPORT_STAFF"
  | "TEACHER";

export type SubjectType = "PRACTICAL" | "THEORY";

export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface AcademicSessionTable {
  branchId: string;
  createdAt: Generated<Timestamp>;
  endDate: Timestamp;
  id: Generated<string>;
  isActive: Generated<boolean>;
  name: string;
  startDate: Timestamp;
}

export interface BranchTable {
  address: string;
  createdAt: Generated<Timestamp>;
  email: string;
  id: Generated<string>;
  instituteId: string;
  isActive: Generated<boolean>;
  isMain: Generated<boolean>;
  name: string;
  phone: string;
}

export interface ClassTable {
  academicSessionId: string;
  createdAt: Generated<Timestamp>;
  feePerMonth: Numeric;
  id: Generated<string>;
  isActive: Generated<boolean>;
  maximumStudents: number;
  name: string;
}

export interface ClassSectionTable {
  classId: string;
  classTeacherId: string;
  createdAt: Generated<Timestamp>;
  id: Generated<string>;
  isActive: Generated<boolean>;
  name: string;
}

export interface EnrollmentTable {
  academicSessionId: string;
  classSectionId: string;
  createdAt: Generated<Timestamp>;
  date: Timestamp;
  id: Generated<string>;
  status: EnrollmentStatus;
  studentId: string;
  type: EnrollmentType;
}

export interface GuardianTable {
  createdAt: Generated<Timestamp>;
  relation: GuardianRelation;
  userId: string;
}

export interface InstituteTable {
  createdAt: Generated<Timestamp>;
  email: string;
  id: Generated<string>;
  isActive: Generated<boolean>;
  isBasicSetupComplete: Generated<boolean>;
  logo: string | null;
  name: string;
  ownerId: string;
}

export interface InstituteOwnerTable {
  createdAt: Generated<Timestamp>;
  hasCompletedSetup: Generated<boolean>;
  userId: string;
}

export interface PlatformAdminTable {
  createdAt: Generated<Timestamp>;
  userId: string;
}

export interface RoleTable {
  code: number;
  createdAt: Generated<Timestamp>;
  description: string;
  id: Generated<string>;
  name: RoleName;
}

export interface SectionSubjectTable {
  academicSessionId: string;
  classSectionId: string;
  createdAt: Generated<Timestamp>;
  id: Generated<string>;
  subjectId: string;
  subjectTeacherId: string;
}

export interface StaffTable {
  branchId: string;
  createdAt: Generated<Timestamp>;
  department: StaffDepartment;
  designation: string;
  id: Generated<string>;
  role: StaffRole;
  salary: Numeric;
  supportStaffProfileId: string | null;
  userId: string | null;
}

export interface StudentTable {
  address: string;
  admissionDate: Timestamp;
  classSectionId: string;
  createdAt: Generated<Timestamp>;
  dateOfBirth: Timestamp;
  email: string | null;
  fatherName: string;
  gender: Gender;
  guardianId: string;
  id: Generated<string>;
  monthlyFee: Numeric;
  name: string;
  photo: string | null;
  previousSchool: string | null;
  registrationNumber: string | null;
  religion: Religion;
}

export interface SubjectTable {
  academicSessionId: string;
  createdAt: Generated<Timestamp>;
  id: Generated<string>;
  isActive: Generated<boolean>;
  marks: number;
  name: string;
  type: SubjectType;
}

export interface SubscriptionTable {
  cancellationDate: Timestamp | null;
  createdAt: Generated<Timestamp>;
  endDate: Timestamp;
  gracePeriodDays: Generated<number>;
  id: Generated<string>;
  lastPaymentDate: Generated<Timestamp>;
  nextPaymentDate: Timestamp;
  ownerId: string;
  paymentCycle: string;
  planId: string;
  startDate: Generated<Timestamp>;
  status: string;
}

export interface SubscriptionPlanTable {
  branches: number;
  createdAt: Generated<Timestamp>;
  description: string | null;
  features: string[];
  id: Generated<string>;
  price: Numeric;
  setupCharges: Numeric;
  students: number;
  title: string;
}

export interface SupportStaffProfileTable {
  address: string;
  cnic: string;
  createdAt: Generated<Timestamp>;
  email: string;
  gender: Gender;
  id: Generated<string>;
  name: string;
  phone: string;
  photo: string | null;
}

export interface UsersTable {
  address: string;
  cnic: string;
  createdAt: Generated<Timestamp>;
  email: string;
  gender: Gender;
  id: Generated<string>;
  isActive: Generated<boolean>;
  isPasswordTemporary: Generated<boolean>;
  name: string;
  password: string;
  phone: string;
  photo: string | null;
  roleId: string;
}

export interface DB {
  academicSession: AcademicSessionTable;
  branch: BranchTable;
  class: ClassTable;
  classSection: ClassSectionTable;
  enrollment: EnrollmentTable;
  guardian: GuardianTable;
  institute: InstituteTable;
  instituteOwner: InstituteOwnerTable;
  platformAdmin: PlatformAdminTable;
  role: RoleTable;
  sectionSubject: SectionSubjectTable;
  staff: StaffTable;
  student: StudentTable;
  subject: SubjectTable;
  subscription: SubscriptionTable;
  subscriptionPlan: SubscriptionPlanTable;
  supportStaffProfile: SupportStaffProfileTable;
  users: UsersTable;
}
