import { type Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  /* ------ Staff Department Type --------- */
  await db.schema
    .createType("staffDepartment")
    .asEnum(["ACADEMIC", "ADMINISTRATION", "SUPPORT"])
    .execute();

  await db.schema
    .createType("staffRole")
    .asEnum(["TEACHER", "SUPPORT_STAFF", "BRANCH_ADMIN", "ACCOUNTANT"])
    .execute();

  await db.schema
    .createTable("staff")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("userId", "uuid", col => col.unique().references("users.id"))
    .addColumn("branchId", "uuid", col => col.notNull().references("branch.id"))
    .addColumn("supportStaffProfileId", "uuid", col =>
      col.unique().references("supportStaffProfile.id"),
    )
    .addColumn("designation", "text", col => col.notNull())
    .addColumn("department", sql`staff_department`, col => col.notNull())
    .addColumn("salary", "numeric(10, 2)", col => col.notNull())
    .addColumn("role", sql`staff_role`, col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addCheckConstraint(
      "staff_is_either_user_or_support_staff",
      sql`(user_id IS NOT NULL AND support_staff_profile_id IS NULL) OR
  (user_id IS NULL AND support_staff_profile_id IS NOT NULL)`,
    )
    .execute();

  await db.schema
    .createIndex("staff_userId_unique_index")
    .on("staff")
    .columns(["userId", "branchId"])
    .unique()
    .execute();

  await db.schema
    .createIndex("staff_supportStaffProfileId_unique_index")
    .on("staff")
    .columns(["supportStaffProfileId", "branchId"])
    .unique()
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("staff").execute();
  await db.schema.dropType("staffDepartment").execute();
  await db.schema.dropType("staffRole").execute();
}
