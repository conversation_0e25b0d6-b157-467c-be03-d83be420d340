import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .createTable("student")
      .addColumn("id", "uuid", col =>
        col.primaryKey().defaultTo(sql`gen_random_uuid()`),
      )
      .addColumn("name", "text", col => col.notNull())
      .addColumn("registrationNumber", "text")
      .addColumn("email", "text", col => col.unique())
      .addColumn("address", "text", col => col.notNull())
      .addColumn("gender", sql`gender`, col => col.notNull())
      .addColumn("photo", "text")
      .addColumn("monthlyFee", "numeric(10, 2)", col => col.notNull())
      .addColumn("admissionDate", "date", col => col.notNull())
      .addColumn("dateOfBirth", "date", col => col.notNull())
      .addColumn("classSectionId", "uuid", col =>
        col
          .notNull()
          .references("classSection.id")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("guardianId", "uuid", col =>
        col
          .notNull()
          .references("guardian.userId")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("createdAt", "timestamptz", col =>
        col.notNull().defaultTo(sql`NOW()`),
      )
      .execute();
  } catch (error: unknown) {
    console.error("Failed to create student table", error);
    throw error;
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema.dropTable("student").execute();
  } catch (error: unknown) {
    console.error("Failed to drop student table", error);
    throw error;
  }
}
