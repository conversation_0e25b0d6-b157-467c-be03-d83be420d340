import { Role } from "../../core/roles/types/roles.types.js";
import { User } from "../../core/users/types/users.types.js";

export type TokenPayload = Pick<User, "email"> & {
  sub: User["id"];
  role: Role["code"];
};

export type VerifiedTokenPayload = TokenPayload & {
  iat: number;
  exp: number;
};

declare module "express-serve-static-core" {
  interface Request {
    user: TokenPayload;
  }
}
