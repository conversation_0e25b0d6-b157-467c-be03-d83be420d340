import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { Request } from "express";
import { TokenPayload } from "../interfaces/auth.interface.js";
import { Reflector } from "@nestjs/core";
import { IS_PUBLIC_KEY } from "../decorators/public.decorator.js";
import { jwtConstants } from "../../core/auth/constants/jwt-constants.js";

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  public async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    // skip authentication check for public routes
    if (isPublic) return true;
    // verify access token
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractFromHeader(request);
    if (!token) throw new UnauthorizedException("Missing access token");
    try {
      const payload = await this.jwtService.verifyAsync<TokenPayload>(token, {
        secret: jwtConstants.accessToken.secret,
      });
      request.user = payload;
    } catch (error) {
      Logger.error(error, "Auth Guard");
      throw new UnauthorizedException("Expired or Invalid access token");
    }
    return true;
  }

  private extractFromHeader(request: Request) {
    const [type, token] = request.headers.authorization?.split(" ") ?? [];
    return type === "Bearer" ? token : undefined;
  }
}
