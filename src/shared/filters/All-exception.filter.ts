import { Logger, Catch, ArgumentsHost, HttpException } from "@nestjs/common";
import { ZodSerializationException } from "nestjs-zod";
import {
  isPostgresException as isPostgresError,
  PostgresException,
} from "../../utils/exception.util.js";
import { BaseExceptionFilter } from "@nestjs/core";

@Catch()
export class AllExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(AllExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    if (exception instanceof ZodSerializationException) {
      const zodError = exception.getZodError();
      this.logger.error(`ZodSerializationException: ${zodError.message}`);
    }

    if (exception instanceof HttpException) {
      super.catch(exception, host);
      return;
    }

    if (isPostgresError(exception)) {
      throw new PostgresException(exception.message, {
        code: exception.code,
        cause: exception.cause,
        severity: exception.severity,
        detail: exception.detail,
        constraint: exception.constraint,
      });
    }
  }
}
